package io.livekit.android.example.voiceassistant.ui.banking.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import io.livekit.android.example.voiceassistant.ui.banking.EmploymentStatus
import io.livekit.android.example.voiceassistant.ui.banking.LoanApplicationState
import io.livekit.android.example.voiceassistant.ui.banking.LoanType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoanApplicationScreen(
    state: LoanApplicationState,
    onStateChange: (LoanApplicationState) -> Unit,
    onSubmit: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isLoanTypeExpanded by remember { mutableStateOf(false) }
    var isEmploymentExpanded by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Progress indicator
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(modifier = Modifier.padding(16.dp)) {
                Text(
                    text = "Step ${state.currentStep} of ${state.totalSteps}",
                    style = MaterialTheme.typography.labelMedium
                )
                LinearProgressIndicator(
                    progress = state.currentStep.toFloat() / state.totalSteps,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }

        // Loan Type
        ExposedDropdownMenuBox(
            expanded = isLoanTypeExpanded,
            onExpandedChange = { isLoanTypeExpanded = it }
        ) {
            OutlinedTextField(
                value = state.loanType.displayName,
                onValueChange = {},
                readOnly = true,
                label = { Text("Loan Type") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isLoanTypeExpanded) },
                modifier = Modifier.fillMaxWidth().menuAnchor()
            )
            ExposedDropdownMenu(
                expanded = isLoanTypeExpanded,
                onDismissRequest = { isLoanTypeExpanded = false }
            ) {
                LoanType.values().forEach { loanType ->
                    DropdownMenuItem(
                        text = { Text(loanType.displayName) },
                        onClick = {
                            onStateChange(state.copy(loanType = loanType))
                            isLoanTypeExpanded = false
                        }
                    )
                }
            }
        }

        // Loan Amount Slider
        Column {
            Text("Requested Amount: $${String.format("%.0f", state.requestedAmount)}")
            Slider(
                value = state.requestedAmount.toFloat(),
                onValueChange = { onStateChange(state.copy(requestedAmount = it.toDouble())) },
                valueRange = 1000f..100000f,
                modifier = Modifier.fillMaxWidth()
            )
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text("$1,000", style = MaterialTheme.typography.bodySmall)
                Text("$100,000", style = MaterialTheme.typography.bodySmall)
            }
        }

        // Annual Income
        OutlinedTextField(
            value = if (state.annualIncome > 0) state.annualIncome.toString() else "",
            onValueChange = { 
                val income = it.toDoubleOrNull() ?: 0.0
                onStateChange(state.copy(annualIncome = income))
            },
            label = { Text("Annual Income") },
            prefix = { Text("$") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            modifier = Modifier.fillMaxWidth()
        )

        // Employment Status
        ExposedDropdownMenuBox(
            expanded = isEmploymentExpanded,
            onExpandedChange = { isEmploymentExpanded = it }
        ) {
            OutlinedTextField(
                value = state.employmentStatus.displayName,
                onValueChange = {},
                readOnly = true,
                label = { Text("Employment Status") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isEmploymentExpanded) },
                modifier = Modifier.fillMaxWidth().menuAnchor()
            )
            ExposedDropdownMenu(
                expanded = isEmploymentExpanded,
                onDismissRequest = { isEmploymentExpanded = false }
            ) {
                EmploymentStatus.values().forEach { status ->
                    DropdownMenuItem(
                        text = { Text(status.displayName) },
                        onClick = {
                            onStateChange(state.copy(employmentStatus = status))
                            isEmploymentExpanded = false
                        }
                    )
                }
            }
        }

        // Pre-approval result
        state.preApprovalResult?.let { result ->
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Pre-Approval Result",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Text("Max Amount: $${String.format("%.2f", result.maxAmount)}")
                    Text("Interest Rate: ${result.interestRate}%")
                    Text("Monthly Payment: $${String.format("%.2f", result.monthlyPayment)}")
                    Text("Status: ${result.eligibility}")
                }
            }
        }

        Button(
            onClick = onSubmit,
            enabled = !state.isSubmitting && state.requestedAmount > 0 && state.annualIncome > 0,
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(if (state.isSubmitting) "Processing..." else "Apply for Loan")
        }
    }
}
