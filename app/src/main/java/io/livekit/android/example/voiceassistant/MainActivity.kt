@file:OptIn(Beta::class)

package io.livekit.android.example.voiceassistant

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.union
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ScrollableTabRow
import androidx.compose.material3.Surface
import androidx.compose.material3.Tab
import androidx.compose.material3.Text
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assistant
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import io.livekit.android.LiveKit
import io.livekit.android.annotations.Beta
import io.livekit.android.compose.local.RoomScope
import io.livekit.android.compose.state.rememberVoiceAssistant
import io.livekit.android.example.voiceassistant.datastreams.rememberChatManager
import io.livekit.android.example.voiceassistant.managers.*
import io.livekit.android.example.voiceassistant.ui.*
import io.livekit.android.example.voiceassistant.ui.VoiceAssistantOverlay
import io.livekit.android.example.voiceassistant.ui.theme.LiveKitVoiceAssistantExampleTheme
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.util.LoggingLevel
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LiveKit.loggingLevel = LoggingLevel.DEBUG
        requireNeededPermissions {
            requireToken { url, token ->
                setContent {
                    LiveKitVoiceAssistantExampleTheme(dynamicColor = false) {
                        Surface {
                            VoiceAssistant(
                                url,
                                token,
                                modifier = Modifier
                                    .fillMaxSize()
                            )
                        }
                    }
                }
            }
        }
    }

    @Composable
    fun VoiceAssistant(url: String, token: String, modifier: Modifier = Modifier) {
        RoomScope(
            url,
            token,
            audio = true,
            connect = true,
        ) { room ->
            val context = LocalContext.current
            val coroutineScope = rememberCoroutineScope()

            // Voice assistant overlay state
            var isVoiceOverlayVisible by remember { mutableStateOf(false) }
            var isVoiceListening by remember { mutableStateOf(false) }

            // Initialize all managers
            val voiceAssistant = rememberVoiceAssistant()
            val chatManager = rememberChatManager(room)
            val rpcManager = rememberRpcManager(room)
            val dataStreamManager = rememberDataStreamManager(room)
            val eventManager = rememberEventManager(room)
            val fileTransferManager = rememberFileTransferManager(room, context)
            val bankingUIManager = io.livekit.android.example.voiceassistant.ui.banking.rememberBankingUIManager(rpcManager)

            // Wake word manager
            val wakeWordManager = rememberWakeWordManager(
                context = context,
                onWakeWordDetected = {
                    BankAssistLogger.logWakeWord("Wake word detected! Showing voice assistant overlay")
                    isVoiceOverlayVisible = true
                    isVoiceListening = true
                },
                onError = { error ->
                    BankAssistLogger.e("Wake word detection error: $error")
                }
            )

            val agentState = voiceAssistant.state
            LaunchedEffect(key1 = agentState) {
                BankAssistLogger.i("Agent state changed: $agentState")
            }

            // Start wake word detection when the room is connected
            LaunchedEffect(room) {
                try {
                    wakeWordManager.startListening()
                    BankAssistLogger.logWakeWord("Wake word detection started")
                } catch (e: Exception) {
                    BankAssistLogger.e("Failed to start wake word detection", e)
                }
            }

            // Tab state
            val tabTitles = listOf("Chat", "RPC", "Files", "Events")
            val pagerState = rememberPagerState(pageCount = { tabTitles.size })

            Box(
                modifier = modifier
                    .fillMaxSize()
                    .windowInsetsPadding(WindowInsets.navigationBars.union(WindowInsets.ime))
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                // Note: Audio visualizer moved to voice assistant overlay

                // Tab row
                ScrollableTabRow(
                    selectedTabIndex = pagerState.currentPage,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    tabTitles.forEachIndexed { index, title ->
                        Tab(
                            selected = pagerState.currentPage == index,
                            onClick = {
                                coroutineScope.launch {
                                    pagerState.animateScrollToPage(index)
                                }
                            },
                            text = { Text(title) }
                        )
                    }
                }

                // Tab content
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier.weight(1f)
                ) { page ->
                    when (page) {
                        0 -> ChatTab(
                            voiceAssistant = voiceAssistant,
                            chatManager = chatManager,
                            room = room,
                            bankingUIManager = bankingUIManager
                        )
                        1 -> RpcPanel(
                            rpcManager = rpcManager,
                            participants = eventManager.getRemoteParticipants()
                        )
                        2 -> FileTransferPanel(
                            fileTransferManager = fileTransferManager
                        )
                        3 -> EventsPanel(
                            eventManager = eventManager
                        )
                    }
                }
                } // End Column

                // Floating Action Button for voice assistant
                FloatingActionButton(
                    onClick = {
                        BankAssistLogger.logVoiceOverlay("FAB clicked! Toggling voice assistant overlay")
                        isVoiceOverlayVisible = !isVoiceOverlayVisible
                        if (isVoiceOverlayVisible) {
                            isVoiceListening = true
                        }
                    },
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(16.dp),
                    containerColor = MaterialTheme.colorScheme.primary,
                    shape = CircleShape
                ) {
                    Icon(
                        imageVector = Icons.Default.Assistant,
                        contentDescription = "Voice Assistant",
                        tint = Color.White
                    )
                }

                // Voice Assistant Overlay
                VoiceAssistantOverlay(
                    isVisible = isVoiceOverlayVisible,
                    isListening = isVoiceListening,
                    transcriptionMessages = (chatManager as io.livekit.android.example.voiceassistant.datastreams.ChatManager).displayMessages,
                    currentTranscription = "", // TODO: Add real-time transcription
                    onDismiss = {
                        BankAssistLogger.logVoiceOverlay("Voice overlay dismissed")
                        isVoiceOverlayVisible = false
                        isVoiceListening = false
                    },
                    onToggleListening = {
                        isVoiceListening = !isVoiceListening
                        BankAssistLogger.logVoiceOverlay("Voice listening toggled: $isVoiceListening")
                    },
                    bankingUIManager = bankingUIManager,
                    voiceAssistant = voiceAssistant // Pass voice assistant for visualization
                )
            } // End Box
        }
    }

    @Composable
    fun ChatTab(
        voiceAssistant: Any, // VoiceAssistant type
        chatManager: Any, // ChatManager type
        room: Any, // Room type
        bankingUIManager: io.livekit.android.example.voiceassistant.ui.banking.BankingUIManager
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            ConstraintLayout(modifier = Modifier.fillMaxSize()) {
                val (chatLog, chatInput) = createRefs()

                // Chat display
                val lazyListState = rememberLazyListState()
                val displayMessages = (chatManager as io.livekit.android.example.voiceassistant.datastreams.ChatManager).displayMessages

                // Auto-scroll to new messages
                LaunchedEffect(displayMessages.size) {
                    if (displayMessages.isNotEmpty()) {
                        lazyListState.animateScrollToItem(displayMessages.size - 1)
                    }
                }

                // Process voice commands for banking
                LaunchedEffect(displayMessages) {
                    displayMessages.lastOrNull()?.let { lastMessage ->
                        if (lastMessage.identity != (room as io.livekit.android.room.Room).localParticipant.identity) {
                            // This is an agent response, check if it contains banking triggers
                            val messageText = lastMessage.text.lowercase()
                            when {
                                messageText.contains("balance") -> bankingUIManager.navigateToScreen(io.livekit.android.example.voiceassistant.ui.banking.BankingScreen.AccountBalance)
                                messageText.contains("transfer") -> bankingUIManager.navigateToScreen(io.livekit.android.example.voiceassistant.ui.banking.BankingScreen.FundTransfer)
                                messageText.contains("loan") -> bankingUIManager.navigateToScreen(io.livekit.android.example.voiceassistant.ui.banking.BankingScreen.LoanApplication)
                                messageText.contains("history") || messageText.contains("transactions") -> bankingUIManager.navigateToScreen(io.livekit.android.example.voiceassistant.ui.banking.BankingScreen.TransactionHistory)
                                messageText.contains("verify") || messageText.contains("authenticate") -> bankingUIManager.navigateToScreen(io.livekit.android.example.voiceassistant.ui.banking.BankingScreen.IdentityVerification)
                                messageText.contains("open account") || messageText.contains("new account") -> bankingUIManager.navigateToScreen(io.livekit.android.example.voiceassistant.ui.banking.BankingScreen.AccountCreation)
                            }
                        } else {
                            // This is a user message, process for voice commands
                            bankingUIManager.processVoiceCommand(lastMessage.text)
                        }
                    }
                }

            LazyColumn(
                userScrollEnabled = true,
                state = lazyListState,
                modifier = Modifier
                    .constrainAs(chatLog) {
                        top.linkTo(parent.top, 8.dp)
                        bottom.linkTo(chatInput.top, 8.dp)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        width = Dimension.fillToConstraints
                        height = Dimension.fillToConstraints
                    }
                    .fillMaxSize()
            ) {
                // Add some sample messages for testing if no messages exist
                if (displayMessages.isEmpty()) {
                    item {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = androidx.compose.ui.Alignment.Center
                        ) {
                            Text(
                                text = "Start a conversation by speaking or typing a message",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                items(
                    items = displayMessages,
                    key = { message -> message.id },
                ) { message ->
                    ChatMessageItem(
                        message = message,
                        isFromUser = message.identity == (room as io.livekit.android.room.Room).localParticipant.identity,
                        modifier = Modifier.animateItem()
                    )
                }
            }

            // Chat input at the bottom
            ChatInput(
                onSendMessage = (chatManager as io.livekit.android.example.voiceassistant.datastreams.ChatManager).onSendTextMessage,
                modifier = Modifier
                    .constrainAs(chatInput) {
                        bottom.linkTo(parent.bottom)
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        width = Dimension.fillToConstraints
                    }
                    .fillMaxWidth()
            )
        } // End ConstraintLayout

        // Banking UI overlay
        io.livekit.android.example.voiceassistant.ui.banking.BankingUIContainer(
            uiManager = bankingUIManager
        )
    } // End Box
}
}

@Preview(showBackground = true)
@Composable
fun VoiceAssistantPreview() {
    LiveKitVoiceAssistantExampleTheme {
        Surface {
            // Note: This is just a UI preview - actual functionality requires LiveKit connection
            Box(modifier = Modifier.fillMaxSize()) {
                ChatInput(
                    onSendMessage = { /* Preview - no action */ },
                    modifier = Modifier.align(androidx.compose.ui.Alignment.BottomCenter)
                )
            }
        }
    }
}
