package io.livekit.android.example.voiceassistant.ui.banking.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import io.livekit.android.example.voiceassistant.ui.banking.AccountCreationState
import io.livekit.android.example.voiceassistant.ui.banking.AccountType

/**
 * Account creation form screen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountCreationScreen(
    state: AccountCreationState,
    onStateChange: (AccountCreationState) -> Unit,
    onSubmit: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isAccountTypeExpanded by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.AccountBalance,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Open a New Account",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "Fill out the form below to get started",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }

        // Personal Information Section
        Text(
            text = "Personal Information",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )

        // First Name
        OutlinedTextField(
            value = state.firstName,
            onValueChange = { onStateChange(state.copy(firstName = it)) },
            label = { Text("First Name") },
            leadingIcon = {
                Icon(Icons.Default.Person, contentDescription = null)
            },
            modifier = Modifier.fillMaxWidth(),
            isError = state.validation.errors.containsKey("firstName"),
            supportingText = state.validation.errors["firstName"]?.let { { Text(it) } }
        )

        // Last Name
        OutlinedTextField(
            value = state.lastName,
            onValueChange = { onStateChange(state.copy(lastName = it)) },
            label = { Text("Last Name") },
            leadingIcon = {
                Icon(Icons.Default.Person, contentDescription = null)
            },
            modifier = Modifier.fillMaxWidth(),
            isError = state.validation.errors.containsKey("lastName"),
            supportingText = state.validation.errors["lastName"]?.let { { Text(it) } }
        )

        // Email
        OutlinedTextField(
            value = state.email,
            onValueChange = { onStateChange(state.copy(email = it)) },
            label = { Text("Email Address") },
            leadingIcon = {
                Icon(Icons.Default.Email, contentDescription = null)
            },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
            modifier = Modifier.fillMaxWidth(),
            isError = state.validation.errors.containsKey("email"),
            supportingText = state.validation.errors["email"]?.let { { Text(it) } }
        )

        // Phone
        OutlinedTextField(
            value = state.phone,
            onValueChange = { onStateChange(state.copy(phone = it)) },
            label = { Text("Phone Number") },
            leadingIcon = {
                Icon(Icons.Default.Phone, contentDescription = null)
            },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
            modifier = Modifier.fillMaxWidth(),
            isError = state.validation.errors.containsKey("phone"),
            supportingText = state.validation.errors["phone"]?.let { { Text(it) } }
        )

        // Account Details Section
        Text(
            text = "Account Details",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )

        // Account Type Dropdown
        ExposedDropdownMenuBox(
            expanded = isAccountTypeExpanded,
            onExpandedChange = { isAccountTypeExpanded = it }
        ) {
            OutlinedTextField(
                value = state.accountType.displayName,
                onValueChange = {},
                readOnly = true,
                label = { Text("Account Type") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isAccountTypeExpanded) },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )
            ExposedDropdownMenu(
                expanded = isAccountTypeExpanded,
                onDismissRequest = { isAccountTypeExpanded = false }
            ) {
                AccountType.values().forEach { accountType ->
                    DropdownMenuItem(
                        text = { Text(accountType.displayName) },
                        onClick = {
                            onStateChange(state.copy(accountType = accountType))
                            isAccountTypeExpanded = false
                        }
                    )
                }
            }
        }

        // Initial Deposit
        OutlinedTextField(
            value = if (state.initialDeposit > 0) state.initialDeposit.toString() else "",
            onValueChange = { 
                val amount = it.toDoubleOrNull() ?: 0.0
                onStateChange(state.copy(initialDeposit = amount))
            },
            label = { Text("Initial Deposit") },
            prefix = { Text("$") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            modifier = Modifier.fillMaxWidth(),
            isError = state.validation.errors.containsKey("initialDeposit"),
            supportingText = state.validation.errors["initialDeposit"]?.let { { Text(it) } }
        )

        // Terms and Conditions
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = state.agreeToTerms,
                onCheckedChange = { onStateChange(state.copy(agreeToTerms = it)) }
            )
            Text(
                text = "I agree to the Terms and Conditions",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(start = 8.dp)
            )
        }

        // Submit Button
        Button(
            onClick = onSubmit,
            enabled = !state.isSubmitting && state.agreeToTerms && 
                     state.firstName.isNotBlank() && state.lastName.isNotBlank() &&
                     state.email.isNotBlank() && state.phone.isNotBlank(),
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (state.isSubmitting) "Creating Account..." else "Create Account"
            )
        }

        // Voice Command Hint
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "💡 Voice Commands",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Text(
                    text = "Try saying: \"Fill in John Doe\" or \"Set account type to savings\"",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
