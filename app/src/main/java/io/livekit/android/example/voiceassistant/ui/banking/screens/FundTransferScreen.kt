package io.livekit.android.example.voiceassistant.ui.banking.screens

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AccountBalance
import androidx.compose.material.icons.filled.AttachMoney
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.SwapHoriz
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import io.livekit.android.example.voiceassistant.ui.banking.FundTransferState
import io.livekit.android.example.voiceassistant.ui.banking.TransferType

/**
 * Fund transfer form screen
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FundTransferScreen(
    state: FundTransferState,
    onStateChange: (FundTransferState) -> Unit,
    onSubmit: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isTransferTypeExpanded by remember { mutableStateOf(false) }
    var isFromAccountExpanded by remember { mutableStateOf(false) }
    var isToAccountExpanded by remember { mutableStateOf(false) }

    // Sample account list (in real app, this would come from API)
    val accounts = listOf(
        "Checking Account (*1234)" to "checking-1234",
        "Savings Account (*5678)" to "savings-5678",
        "Business Account (*9012)" to "business-9012"
    )

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    imageVector = Icons.Default.SwapHoriz,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Transfer Funds",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                Text(
                    text = "Move money between accounts securely",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }

        // Transfer Type
        ExposedDropdownMenuBox(
            expanded = isTransferTypeExpanded,
            onExpandedChange = { isTransferTypeExpanded = it }
        ) {
            OutlinedTextField(
                value = state.transferType.displayName,
                onValueChange = {},
                readOnly = true,
                label = { Text("Transfer Type") },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isTransferTypeExpanded) },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor()
            )
            ExposedDropdownMenu(
                expanded = isTransferTypeExpanded,
                onDismissRequest = { isTransferTypeExpanded = false }
            ) {
                TransferType.values().forEach { transferType ->
                    DropdownMenuItem(
                        text = { Text(transferType.displayName) },
                        onClick = {
                            onStateChange(state.copy(transferType = transferType))
                            isTransferTypeExpanded = false
                        }
                    )
                }
            }
        }

        // From Account
        ExposedDropdownMenuBox(
            expanded = isFromAccountExpanded,
            onExpandedChange = { isFromAccountExpanded = it }
        ) {
            OutlinedTextField(
                value = accounts.find { it.second == state.fromAccount }?.first ?: "Select Account",
                onValueChange = {},
                readOnly = true,
                label = { Text("From Account") },
                leadingIcon = { Icon(Icons.Default.AccountBalance, contentDescription = null) },
                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isFromAccountExpanded) },
                modifier = Modifier
                    .fillMaxWidth()
                    .menuAnchor(),
                isError = state.validation.errors.containsKey("fromAccount")
            )
            ExposedDropdownMenu(
                expanded = isFromAccountExpanded,
                onDismissRequest = { isFromAccountExpanded = false }
            ) {
                accounts.forEach { (displayName, accountId) ->
                    DropdownMenuItem(
                        text = { Text(displayName) },
                        onClick = {
                            onStateChange(state.copy(fromAccount = accountId))
                            isFromAccountExpanded = false
                        }
                    )
                }
            }
        }

        // To Account (for internal transfers) or Recipient Details (for external)
        if (state.transferType == TransferType.INTERNAL) {
            ExposedDropdownMenuBox(
                expanded = isToAccountExpanded,
                onExpandedChange = { isToAccountExpanded = it }
            ) {
                OutlinedTextField(
                    value = accounts.find { it.second == state.toAccount }?.first ?: "Select Account",
                    onValueChange = {},
                    readOnly = true,
                    label = { Text("To Account") },
                    leadingIcon = { Icon(Icons.Default.AccountBalance, contentDescription = null) },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = isToAccountExpanded) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor(),
                    isError = state.validation.errors.containsKey("toAccount")
                )
                ExposedDropdownMenu(
                    expanded = isToAccountExpanded,
                    onDismissRequest = { isToAccountExpanded = false }
                ) {
                    accounts.filter { it.second != state.fromAccount }.forEach { (displayName, accountId) ->
                        DropdownMenuItem(
                            text = { Text(displayName) },
                            onClick = {
                                onStateChange(state.copy(toAccount = accountId))
                                isToAccountExpanded = false
                            }
                        )
                    }
                }
            }
        } else {
            // External transfer recipient details
            OutlinedTextField(
                value = state.recipientName,
                onValueChange = { onStateChange(state.copy(recipientName = it)) },
                label = { Text("Recipient Name") },
                leadingIcon = { Icon(Icons.Default.Person, contentDescription = null) },
                modifier = Modifier.fillMaxWidth(),
                isError = state.validation.errors.containsKey("recipientName")
            )

            OutlinedTextField(
                value = state.toAccount,
                onValueChange = { onStateChange(state.copy(toAccount = it)) },
                label = { Text("Recipient Account Number") },
                leadingIcon = { Icon(Icons.Default.AccountBalance, contentDescription = null) },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.fillMaxWidth(),
                isError = state.validation.errors.containsKey("toAccount")
            )

            if (state.transferType == TransferType.WIRE || state.transferType == TransferType.ACH) {
                OutlinedTextField(
                    value = state.routingNumber,
                    onValueChange = { onStateChange(state.copy(routingNumber = it)) },
                    label = { Text("Routing Number") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    modifier = Modifier.fillMaxWidth(),
                    isError = state.validation.errors.containsKey("routingNumber")
                )
            }
        }

        // Amount
        OutlinedTextField(
            value = if (state.amount > 0) state.amount.toString() else "",
            onValueChange = { 
                val amount = it.toDoubleOrNull() ?: 0.0
                onStateChange(state.copy(amount = amount))
            },
            label = { Text("Amount") },
            leadingIcon = { Icon(Icons.Default.AttachMoney, contentDescription = null) },
            prefix = { Text("$") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            modifier = Modifier.fillMaxWidth(),
            isError = state.validation.errors.containsKey("amount"),
            supportingText = state.validation.errors["amount"]?.let { { Text(it) } }
        )

        // Description
        OutlinedTextField(
            value = state.description,
            onValueChange = { onStateChange(state.copy(description = it)) },
            label = { Text("Description (Optional)") },
            leadingIcon = { Icon(Icons.Default.Description, contentDescription = null) },
            modifier = Modifier.fillMaxWidth(),
            maxLines = 3
        )

        // Fee Information
        if (state.estimatedFee > 0) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text(
                        text = "Transfer Summary",
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.SemiBold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("Transfer Amount:")
                        Text("$${String.format("%.2f", state.amount)}")
                    }
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text("Estimated Fee:")
                        Text("$${String.format("%.2f", state.estimatedFee)}")
                    }
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            "Total:",
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            "$${String.format("%.2f", state.amount + state.estimatedFee)}",
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        }

        // Submit Button
        Button(
            onClick = onSubmit,
            enabled = !state.isSubmitting && state.amount > 0 && 
                     state.fromAccount.isNotBlank() && state.toAccount.isNotBlank(),
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = if (state.isSubmitting) "Processing Transfer..." else "Transfer Funds"
            )
        }

        // Voice Command Hint
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(modifier = Modifier.padding(12.dp)) {
                Text(
                    text = "💡 Voice Commands",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.SemiBold
                )
                Text(
                    text = "Try saying: \"Transfer $500 from checking to savings\" or \"Send $100 to John\"",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
