package io.livekit.android.example.voiceassistant.ui.banking

import androidx.compose.runtime.Stable
import io.livekit.android.example.voiceassistant.managers.AccountBalanceResponse
import io.livekit.android.example.voiceassistant.managers.LoanInfoResponse
import io.livekit.android.example.voiceassistant.managers.Transaction
import io.livekit.android.example.voiceassistant.managers.TransferFundsResponse
import io.livekit.android.example.voiceassistant.managers.VerifyIdentityResponse

/**
 * Represents different banking screens that can be triggered by voice commands or RPC calls
 */
sealed class BankingScreen {
    object None : BankingScreen()
    object AccountCreation : BankingScreen()
    object FundTransfer : BankingScreen()
    object LoanApplication : BankingScreen()
    object TransactionHistory : BankingScreen()
    object IdentityVerification : BankingScreen()
    object AccountBalance : BankingScreen()
    object PinEntry : BankingScreen()
    object BiometricAuth : BankingScreen()
}

/**
 * Authentication state for banking operations
 */
@Stable
data class AuthenticationState(
    val isAuthenticated: Boolean = false,
    val authMethod: AuthMethod? = null,
    val sessionExpiry: Long? = null,
    val failedAttempts: Int = 0,
    val isLocked: Boolean = false
)

enum class AuthMethod {
    PIN,
    FINGERPRINT,
    FACE,
    VOICE
}

/**
 * Form validation state
 */
@Stable
data class FormValidationState(
    val isValid: Boolean = false,
    val errors: Map<String, String> = emptyMap(),
    val warnings: Map<String, String> = emptyMap()
)

/**
 * Account creation form state
 */
@Stable
data class AccountCreationState(
    val firstName: String = "",
    val lastName: String = "",
    val email: String = "",
    val phone: String = "",
    val accountType: AccountType = AccountType.CHECKING,
    val initialDeposit: Double = 0.0,
    val agreeToTerms: Boolean = false,
    val validation: FormValidationState = FormValidationState(),
    val isSubmitting: Boolean = false
)

enum class AccountType(val displayName: String) {
    CHECKING("Checking Account"),
    SAVINGS("Savings Account"),
    BUSINESS("Business Account"),
    STUDENT("Student Account")
}

/**
 * Fund transfer form state
 */
@Stable
data class FundTransferState(
    val fromAccount: String = "",
    val toAccount: String = "",
    val amount: Double = 0.0,
    val description: String = "",
    val transferType: TransferType = TransferType.INTERNAL,
    val recipientName: String = "",
    val recipientBank: String = "",
    val routingNumber: String = "",
    val validation: FormValidationState = FormValidationState(),
    val isSubmitting: Boolean = false,
    val requiresConfirmation: Boolean = false,
    val estimatedFee: Double = 0.0
)

enum class TransferType(val displayName: String) {
    INTERNAL("Between My Accounts"),
    EXTERNAL("To External Account"),
    WIRE("Wire Transfer"),
    ACH("ACH Transfer")
}

/**
 * Loan application form state
 */
@Stable
data class LoanApplicationState(
    val loanType: LoanType = LoanType.PERSONAL,
    val requestedAmount: Double = 0.0,
    val purpose: String = "",
    val annualIncome: Double = 0.0,
    val employmentStatus: EmploymentStatus = EmploymentStatus.EMPLOYED,
    val creditScore: Int? = null,
    val termMonths: Int = 12,
    val validation: FormValidationState = FormValidationState(),
    val isSubmitting: Boolean = false,
    val preApprovalResult: LoanInfoResponse? = null,
    val currentStep: Int = 1,
    val totalSteps: Int = 4
)

enum class LoanType(val displayName: String) {
    PERSONAL("Personal Loan"),
    MORTGAGE("Home Mortgage"),
    AUTO("Auto Loan"),
    BUSINESS("Business Loan"),
    STUDENT("Student Loan")
}

enum class EmploymentStatus(val displayName: String) {
    EMPLOYED("Employed"),
    SELF_EMPLOYED("Self-Employed"),
    UNEMPLOYED("Unemployed"),
    RETIRED("Retired"),
    STUDENT("Student")
}

/**
 * Transaction history filter state
 */
@Stable
data class TransactionHistoryState(
    val accountId: String = "primary",
    val startDate: Long? = null,
    val endDate: Long? = null,
    val transactionType: TransactionType? = null,
    val minAmount: Double? = null,
    val maxAmount: Double? = null,
    val searchQuery: String = "",
    val transactions: List<Transaction> = emptyList(),
    val isLoading: Boolean = false,
    val hasMore: Boolean = false
)

enum class TransactionType(val displayName: String) {
    ALL("All Transactions"),
    DEPOSIT("Deposits"),
    WITHDRAWAL("Withdrawals"),
    TRANSFER("Transfers"),
    PAYMENT("Payments"),
    FEE("Fees")
}

/**
 * Identity verification state
 */
@Stable
data class IdentityVerificationState(
    val verificationType: VerificationType = VerificationType.FINGERPRINT,
    val isInProgress: Boolean = false,
    val result: VerifyIdentityResponse? = null,
    val retryCount: Int = 0,
    val maxRetries: Int = 3
)

enum class VerificationType(val displayName: String) {
    FINGERPRINT("Fingerprint"),
    FACE("Face Recognition"),
    VOICE("Voice Recognition"),
    PIN("PIN Verification")
}

/**
 * Account balance display state
 */
@Stable
data class AccountBalanceState(
    val balances: List<AccountBalanceResponse> = emptyList(),
    val selectedAccountId: String = "primary",
    val isLoading: Boolean = false,
    val lastUpdated: Long? = null
)

/**
 * Overall banking UI state
 */
@Stable
data class BankingUIState(
    val currentScreen: BankingScreen = BankingScreen.None,
    val authState: AuthenticationState = AuthenticationState(),
    val accountCreation: AccountCreationState = AccountCreationState(),
    val fundTransfer: FundTransferState = FundTransferState(),
    val loanApplication: LoanApplicationState = LoanApplicationState(),
    val transactionHistory: TransactionHistoryState = TransactionHistoryState(),
    val identityVerification: IdentityVerificationState = IdentityVerificationState(),
    val accountBalance: AccountBalanceState = AccountBalanceState(),
    val isVoiceInputActive: Boolean = false,
    val lastVoiceCommand: String? = null,
    val rpcCallInProgress: Boolean = false,
    val errorMessage: String? = null
)

/**
 * Banking UI events
 */
sealed class BankingUIEvent {
    object NavigateBack : BankingUIEvent()
    data class NavigateToScreen(val screen: BankingScreen) : BankingUIEvent()
    data class UpdateAccountCreation(val state: AccountCreationState) : BankingUIEvent()
    data class UpdateFundTransfer(val state: FundTransferState) : BankingUIEvent()
    data class UpdateLoanApplication(val state: LoanApplicationState) : BankingUIEvent()
    data class UpdateTransactionHistory(val state: TransactionHistoryState) : BankingUIEvent()
    data class UpdateIdentityVerification(val state: IdentityVerificationState) : BankingUIEvent()
    data class UpdateAccountBalance(val state: AccountBalanceState) : BankingUIEvent()
    data class ProcessVoiceCommand(val command: String) : BankingUIEvent()
    data class ShowError(val message: String) : BankingUIEvent()
    object ClearError : BankingUIEvent()
    data class SubmitForm(val screen: BankingScreen) : BankingUIEvent()
    object StartAuthentication : BankingUIEvent()
    data class CompleteAuthentication(val success: Boolean, val method: AuthMethod) : BankingUIEvent()
}
