package io.livekit.android.example.voiceassistant.managers

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.room.Room
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.encodeToString

/**
 * Data classes for RPC requests and responses
 */
@Serializable
data class LocationRequest(val requestId: String = "")

@Serializable
data class LocationResponse(
    val latitude: Double,
    val longitude: Double,
    val accuracy: Float,
    val timestamp: Long
)

@Serializable
data class TimeRequest(val requestId: String = "")

@Serializable
data class TimeResponse(
    val currentTime: Long,
    val timezone: String,
    val formattedTime: String
)

@Serializable
data class PermissionRequest(
    val permissionType: String,
    val reason: String = ""
)

@Serializable
data class PermissionResponse(
    val granted: Boolean,
    val permissionType: String
)

@Serializable
data class DeviceInfoRequest(val requestId: String = "")

@Serializable
data class DeviceInfoResponse(
    val deviceModel: String,
    val osVersion: String,
    val appVersion: String,
    val batteryLevel: Int,
    val networkType: String
)

/**
 * Authentication RPC data classes
 */
@Serializable
data class AuthenticateRequest(
    val user_id: String,
    val phone_last_4: String
)

@Serializable
data class AuthenticateResponse(
    val success: Boolean,
    val user_id: String,
    val session_token: String? = null,
    val expires_at: Long? = null,
    val message: String,
    val phone_last_4: String
)

/**
 * Notification RPC data classes
 */
@Serializable
data class NotificationRequest(
    val type: String, // "welcome", "transfer_confirmation", "alert", "info"
    val title: String,
    val message: String,
    val data: Map<String, String> = emptyMap()
)

@Serializable
data class NotificationResponse(
    val success: Boolean,
    val notification_id: String,
    val timestamp: Long
)

/**
 * Banking-specific RPC data classes
 */
@Serializable
data class AccountBalanceRequest(val accountId: String = "")

@Serializable
data class AccountBalanceResponse(
    val accountId: String,
    val balance: Double,
    val currency: String,
    val accountType: String,
    val lastUpdated: Long
)

@Serializable
data class TransferFundsRequest(
    val fromAccountId: String,
    val toAccountId: String,
    val amount: Double,
    val currency: String = "USD",
    val description: String = ""
)

@Serializable
data class TransferFundsResponse(
    val transactionId: String,
    val status: String,
    val fromAccountId: String,
    val toAccountId: String,
    val amount: Double,
    val currency: String,
    val timestamp: Long
)

@Serializable
data class TransactionHistoryRequest(
    val accountId: String,
    val limit: Int = 10,
    val startDate: Long? = null,
    val endDate: Long? = null
)

@Serializable
data class Transaction(
    val id: String,
    val type: String,
    val amount: Double,
    val currency: String,
    val description: String,
    val timestamp: Long,
    val balance: Double
)

@Serializable
data class TransactionHistoryResponse(
    val accountId: String,
    val transactions: List<Transaction>
)

@Serializable
data class VerifyIdentityRequest(
    val biometricData: String,
    val verificationType: String // "fingerprint", "face", "voice"
)

@Serializable
data class VerifyIdentityResponse(
    val verified: Boolean,
    val confidence: Float,
    val verificationType: String,
    val timestamp: Long
)

@Serializable
data class LoanInfoRequest(
    val loanType: String, // "personal", "mortgage", "auto", "business"
    val amount: Double? = null
)

@Serializable
data class LoanInfoResponse(
    val loanType: String,
    val maxAmount: Double,
    val interestRate: Float,
    val termMonths: Int,
    val monthlyPayment: Double,
    val eligibility: String,
    val requirements: List<String>
)

/**
 * RPC call result for UI display
 */
data class RpcCall(
    val id: String,
    val method: String,
    val participant: Participant.Identity,
    val timestamp: Long,
    val status: RpcStatus,
    val request: String,
    val response: String? = null,
    val error: String? = null
)

enum class RpcStatus {
    PENDING,
    SUCCESS,
    ERROR,
    TIMEOUT
}

/**
 * Manages RPC (Remote Procedure Call) functionality for LiveKit
 */
class RpcManager(
    private val room: Room,
    private val json: Json = Json { ignoreUnknownKeys = true }
) {
    private val _rpcCalls = mutableStateListOf<RpcCall>()
    val rpcCalls: List<RpcCall> = _rpcCalls

    /**
     * Register all RPC methods that this participant can handle
     * Note: This is a simplified implementation for demonstration
     */
    fun registerRpcMethods() {
        try {
            // For now, we'll log that RPC methods would be registered
            // In a real implementation with the correct LiveKit Android SDK API:
            // room.localParticipant.registerRpcMethod("getUserLocation") { invocation ->
            //     handleGetUserLocation(invocation.payload)
            // }

            Timber.i { "RPC method registration placeholder - would register getUserLocation, getUserTime, requestPermission, getDeviceInfo, getAccountBalance, transferFunds, getTransactionHistory, verifyIdentity, requestLoanInfo" }
        } catch (e: Exception) {
            Timber.e(e) { "Failed to register RPC methods" }
        }
    }

    /**
     * Authentication RPC Methods
     */

    /**
     * Call client.authenticate on a remote participant
     */
    suspend fun callAuthenticate(
        participantIdentity: Participant.Identity,
        userId: String,
        phoneLast4: String
    ): AuthenticateResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.authenticate",
            request = AuthenticateRequest(userId, phoneLast4),
            responseType = AuthenticateResponse::class
        )
    }

    /**
     * Call client.notification on a remote participant
     */
    suspend fun callNotification(
        participantIdentity: Participant.Identity,
        type: String,
        title: String,
        message: String,
        data: Map<String, String> = emptyMap()
    ): NotificationResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.notification",
            request = NotificationRequest(type, title, message, data),
            responseType = NotificationResponse::class
        )
    }

    /**
     * Call getUserLocation on a remote participant
     */
    suspend fun callGetUserLocation(participantIdentity: Participant.Identity): LocationResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.getUserLocation",
            request = LocationRequest(),
            responseType = LocationResponse::class
        )
    }

    /**
     * Call getUserTime on a remote participant
     */
    suspend fun callGetUserTime(participantIdentity: Participant.Identity): TimeResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.getUserTime",
            request = TimeRequest(),
            responseType = TimeResponse::class
        )
    }

    /**
     * Call requestPermission on a remote participant
     */
    suspend fun callRequestPermission(
        participantIdentity: Participant.Identity,
        permissionType: String,
        reason: String = ""
    ): PermissionResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.requestPermission",
            request = PermissionRequest(permissionType, reason),
            responseType = PermissionResponse::class
        )
    }

    /**
     * Call getDeviceInfo on a remote participant
     */
    suspend fun callGetDeviceInfo(participantIdentity: Participant.Identity): DeviceInfoResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.getDeviceInfo",
            request = DeviceInfoRequest(),
            responseType = DeviceInfoResponse::class
        )
    }

    /**
     * Banking RPC Methods
     */

    /**
     * Call getAccountBalance on a remote participant
     */
    suspend fun callGetAccountBalance(
        participantIdentity: Participant.Identity,
        accountId: String = "primary"
    ): AccountBalanceResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.getAccountBalance",
            request = AccountBalanceRequest(accountId),
            responseType = AccountBalanceResponse::class
        )
    }

    /**
     * Call transferFunds on a remote participant
     */
    suspend fun callTransferFunds(
        participantIdentity: Participant.Identity,
        fromAccountId: String,
        toAccountId: String,
        amount: Double,
        description: String = ""
    ): TransferFundsResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.transferFunds",
            request = TransferFundsRequest(fromAccountId, toAccountId, amount, "USD", description),
            responseType = TransferFundsResponse::class
        )
    }

    /**
     * Call getTransactionHistory on a remote participant
     */
    suspend fun callGetTransactionHistory(
        participantIdentity: Participant.Identity,
        accountId: String = "primary",
        limit: Int = 10
    ): TransactionHistoryResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.getTransactionHistory",
            request = TransactionHistoryRequest(accountId, limit),
            responseType = TransactionHistoryResponse::class
        )
    }

    /**
     * Call verifyIdentity on a remote participant
     */
    suspend fun callVerifyIdentity(
        participantIdentity: Participant.Identity,
        biometricData: String,
        verificationType: String = "fingerprint"
    ): VerifyIdentityResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.verifyIdentity",
            request = VerifyIdentityRequest(biometricData, verificationType),
            responseType = VerifyIdentityResponse::class
        )
    }

    /**
     * Call requestLoanInfo on a remote participant
     */
    suspend fun callRequestLoanInfo(
        participantIdentity: Participant.Identity,
        loanType: String,
        amount: Double? = null
    ): LoanInfoResponse? {
        return performRpcCall(
            participantIdentity = participantIdentity,
            method = "client.requestLoanInfo",
            request = LoanInfoRequest(loanType, amount),
            responseType = LoanInfoResponse::class
        )
    }

    /**
     * Generic RPC call performer with error handling and logging
     */
    private suspend inline fun <reified T : Any, reified R : Any> performRpcCall(
        participantIdentity: Participant.Identity,
        method: String,
        request: T,
        responseType: kotlin.reflect.KClass<R>
    ): R? {
        val callId = generateCallId()
        val requestJson = json.encodeToString(request)
        
        val rpcCall = RpcCall(
            id = callId,
            method = method,
            participant = participantIdentity,
            timestamp = System.currentTimeMillis(),
            status = RpcStatus.PENDING,
            request = requestJson
        )
        _rpcCalls.add(rpcCall)

        return try {
            Timber.d { "Performing RPC call $method to $participantIdentity with payload: $requestJson" }

            // For now, simulate RPC call with mock response
            // In a real implementation:
            // val responseJson = room.localParticipant.performRpc(
            //     destinationIdentity = participantIdentity,
            //     method = method,
            //     payload = requestJson,
            //     responseTimeout = 10.seconds
            // )

            val responseJson = when (method) {
                // Authentication methods
                "client.authenticate" -> json.encodeToString(AuthenticateResponse(
                    success = true,
                    user_id = "user123",
                    session_token = "session_${System.currentTimeMillis()}",
                    expires_at = System.currentTimeMillis() + (30 * 60 * 1000), // 30 minutes
                    message = "Authentication successful",
                    phone_last_4 = "7890"
                ))
                "client.notification" -> json.encodeToString(NotificationResponse(
                    success = true,
                    notification_id = "notif_${System.currentTimeMillis()}",
                    timestamp = System.currentTimeMillis()
                ))

                // General methods
                "client.getUserLocation" -> json.encodeToString(LocationResponse(37.7749, -122.4194, 10.0f, System.currentTimeMillis()))
                "client.getUserTime" -> json.encodeToString(TimeResponse(System.currentTimeMillis(), "UTC", "Mock Time"))
                "client.getDeviceInfo" -> json.encodeToString(DeviceInfoResponse("Mock Device", "Android 14", "1.0.0", 85, "WiFi"))
                "client.requestPermission" -> json.encodeToString(PermissionResponse(true, "camera"))

                // Banking methods
                "client.getAccountBalance" -> json.encodeToString(AccountBalanceResponse(
                    accountId = "primary",
                    balance = 15420.75,
                    currency = "USD",
                    accountType = "Checking",
                    lastUpdated = System.currentTimeMillis()
                ))
                "client.transferFunds" -> json.encodeToString(TransferFundsResponse(
                    transactionId = "TXN${System.currentTimeMillis()}",
                    status = "completed",
                    fromAccountId = "primary",
                    toAccountId = "savings",
                    amount = 500.0,
                    currency = "USD",
                    timestamp = System.currentTimeMillis()
                ))
                "client.getTransactionHistory" -> json.encodeToString(TransactionHistoryResponse(
                    accountId = "primary",
                    transactions = listOf(
                        Transaction("1", "deposit", 2500.0, "USD", "Salary deposit", System.currentTimeMillis() - ********, 15420.75),
                        Transaction("2", "withdrawal", -45.50, "USD", "Coffee shop", System.currentTimeMillis() - ********, 12920.75),
                        Transaction("3", "transfer", -500.0, "USD", "Transfer to savings", System.currentTimeMillis() - ********, 12420.75)
                    )
                ))
                "client.verifyIdentity" -> json.encodeToString(VerifyIdentityResponse(
                    verified = true,
                    confidence = 0.95f,
                    verificationType = "fingerprint",
                    timestamp = System.currentTimeMillis()
                ))
                "client.requestLoanInfo" -> json.encodeToString(LoanInfoResponse(
                    loanType = "personal",
                    maxAmount = 50000.0,
                    interestRate = 5.99f,
                    termMonths = 60,
                    monthlyPayment = 966.64,
                    eligibility = "approved",
                    requirements = listOf("Valid ID", "Proof of income", "Credit check")
                ))

                else -> "{\"error\":\"Unknown method\"}"
            }
            
            val response = json.decodeFromString<R>(responseJson)
            
            // Update call status
            val index = _rpcCalls.indexOfFirst { it.id == callId }
            if (index >= 0) {
                _rpcCalls[index] = _rpcCalls[index].copy(
                    status = RpcStatus.SUCCESS,
                    response = responseJson
                )
            }
            
            Timber.d { "RPC call $method completed successfully: $responseJson" }
            response
            
        } catch (e: Exception) {
            Timber.e(e) { "RPC call $method failed: ${e.message}" }
            
            // Update call status
            val index = _rpcCalls.indexOfFirst { it.id == callId }
            if (index >= 0) {
                _rpcCalls[index] = _rpcCalls[index].copy(
                    status = RpcStatus.ERROR,
                    error = e.message
                )
            }
            null
        }
    }

    /**
     * Handle incoming getUserLocation RPC calls
     */
    private fun handleGetUserLocation(payload: String): String {
        return try {
            val request = json.decodeFromString<LocationRequest>(payload)

            // Mock location data - in a real app, you would get actual location
            val response = LocationResponse(
                latitude = 37.7749,
                longitude = -122.4194,
                accuracy = 10.0f,
                timestamp = System.currentTimeMillis()
            )

            json.encodeToString(response)
        } catch (e: Exception) {
            Timber.e(e) { "Error handling getUserLocation RPC" }
            throw Exception("Failed to get user location: ${e.message}")
        }
    }

    /**
     * Handle incoming getUserTime RPC calls
     */
    private fun handleGetUserTime(payload: String): String {
        return try {
            val request = json.decodeFromString<TimeRequest>(payload)
            
            val currentTime = System.currentTimeMillis()
            val response = TimeResponse(
                currentTime = currentTime,
                timezone = java.util.TimeZone.getDefault().id,
                formattedTime = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                    .format(java.util.Date(currentTime))
            )
            
            json.encodeToString(response)
        } catch (e: Exception) {
            Timber.e(e) { "Error handling getUserTime RPC" }
            throw Exception("Failed to get user time: ${e.message}")
        }
    }

    /**
     * Handle incoming requestPermission RPC calls
     */
    private fun handleRequestPermission(payload: String): String {
        return try {
            val request = json.decodeFromString<PermissionRequest>(payload)
            
            // Mock permission response - in a real app, you would check actual permissions
            val response = PermissionResponse(
                granted = true, // Mock: always grant for demo
                permissionType = request.permissionType
            )
            
            json.encodeToString(response)
        } catch (e: Exception) {
            Timber.e(e) { "Error handling requestPermission RPC" }
            throw Exception("Failed to handle permission request: ${e.message}")
        }
    }

    /**
     * Handle incoming getDeviceInfo RPC calls
     */
    private fun handleGetDeviceInfo(payload: String): String {
        return try {
            val request = json.decodeFromString<DeviceInfoRequest>(payload)
            
            val response = DeviceInfoResponse(
                deviceModel = android.os.Build.MODEL,
                osVersion = android.os.Build.VERSION.RELEASE,
                appVersion = "1.0.0", // You would get this from BuildConfig
                batteryLevel = 85, // Mock battery level
                networkType = "WiFi" // Mock network type
            )
            
            json.encodeToString(response)
        } catch (e: Exception) {
            Timber.e(e) { "Error handling getDeviceInfo RPC" }
            throw Exception("Failed to get device info: ${e.message}")
        }
    }

    private fun generateCallId(): String {
        return "rpc_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    fun cleanup() {
        try {
            // Unregister RPC methods if needed
            Timber.d { "Cleaning up RPC manager" }
        } catch (e: Exception) {
            Timber.e(e) { "Error during RPC manager cleanup" }
        }
    }
}

/**
 * Composable function to create and manage RpcManager
 */
@Composable
fun rememberRpcManager(room: Room): RpcManager {
    val coroutineScope = rememberCoroutineScope()
    
    val rpcManager = remember(room) {
        RpcManager(room).apply {
            coroutineScope.launch {
                registerRpcMethods()
            }
        }
    }

    DisposableEffect(room) {
        onDispose {
            rpcManager.cleanup()
        }
    }

    return rpcManager
}
