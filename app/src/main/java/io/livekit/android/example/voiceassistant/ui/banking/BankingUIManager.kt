package io.livekit.android.example.voiceassistant.ui.banking

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import io.livekit.android.example.voiceassistant.managers.RpcManager
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.regex.Pattern

/**
 * Manages banking UI state and voice command processing
 */
@Composable
fun rememberBankingUIManager(
    rpcManager: RpcManager
): BankingUIManager {
    val coroutineScope = rememberCoroutineScope()
    var uiState by remember { mutableStateOf(BankingUIState()) }

    val manager = remember {
        BankingUIManager(
            state = uiState,
            onEvent = { event ->
                coroutineScope.launch {
                    uiState = handleBankingUIEvent(uiState, event, rpcManager)
                }
            }
        )
    }

    // Update manager state when uiState changes
    LaunchedEffect(uiState) {
        manager.updateState(uiState)
    }

    return manager
}

/**
 * Banking UI Manager class
 */
class BankingUIManager(
    private var state: BankingUIState,
    private val onEvent: (BankingUIEvent) -> Unit
) {
    fun updateState(newState: BankingUIState) {
        state = newState
    }

    fun getCurrentState(): BankingUIState = state

    fun handleEvent(event: BankingUIEvent) {
        onEvent(event)
    }

    /**
     * Process voice commands and trigger appropriate UI screens
     */
    fun processVoiceCommand(command: String) {
        onEvent(BankingUIEvent.ProcessVoiceCommand(command))
    }

    /**
     * Navigate to a specific banking screen
     */
    fun navigateToScreen(screen: BankingScreen) {
        onEvent(BankingUIEvent.NavigateToScreen(screen))
    }

    /**
     * Submit current form
     */
    fun submitCurrentForm() {
        onEvent(BankingUIEvent.SubmitForm(state.currentScreen))
    }

    /**
     * Start authentication process
     */
    fun startAuthentication() {
        onEvent(BankingUIEvent.StartAuthentication)
    }
}

/**
 * Handle banking UI events and return updated state
 */
private suspend fun handleBankingUIEvent(
    currentState: BankingUIState,
    event: BankingUIEvent,
    rpcManager: RpcManager
): BankingUIState {
    return when (event) {
        is BankingUIEvent.NavigateBack -> {
            currentState.copy(currentScreen = BankingScreen.None)
        }

        is BankingUIEvent.NavigateToScreen -> {
            currentState.copy(currentScreen = event.screen)
        }

        is BankingUIEvent.UpdateAccountCreation -> {
            currentState.copy(accountCreation = event.state)
        }

        is BankingUIEvent.UpdateFundTransfer -> {
            currentState.copy(fundTransfer = event.state)
        }

        is BankingUIEvent.UpdateLoanApplication -> {
            currentState.copy(loanApplication = event.state)
        }

        is BankingUIEvent.UpdateTransactionHistory -> {
            currentState.copy(transactionHistory = event.state)
        }

        is BankingUIEvent.UpdateIdentityVerification -> {
            currentState.copy(identityVerification = event.state)
        }

        is BankingUIEvent.UpdateAccountBalance -> {
            currentState.copy(accountBalance = event.state)
        }

        is BankingUIEvent.ProcessVoiceCommand -> {
            processVoiceCommandInternal(currentState, event.command)
        }

        is BankingUIEvent.ShowError -> {
            currentState.copy(errorMessage = event.message)
        }

        is BankingUIEvent.ClearError -> {
            currentState.copy(errorMessage = null)
        }

        is BankingUIEvent.SubmitForm -> {
            handleFormSubmission(currentState, event.screen, rpcManager)
        }

        is BankingUIEvent.StartAuthentication -> {
            currentState.copy(
                currentScreen = BankingScreen.PinEntry,
                authState = currentState.authState.copy(isAuthenticated = false)
            )
        }

        is BankingUIEvent.CompleteAuthentication -> {
            if (event.success) {
                currentState.copy(
                    authState = currentState.authState.copy(
                        isAuthenticated = true,
                        authMethod = event.method,
                        sessionExpiry = System.currentTimeMillis() + (30 * 60 * 1000), // 30 minutes
                        failedAttempts = 0
                    ),
                    currentScreen = BankingScreen.None
                )
            } else {
                val newFailedAttempts = currentState.authState.failedAttempts + 1
                currentState.copy(
                    authState = currentState.authState.copy(
                        failedAttempts = newFailedAttempts,
                        isLocked = newFailedAttempts >= 3
                    ),
                    errorMessage = if (newFailedAttempts >= 3) "Account locked due to too many failed attempts" else "Authentication failed"
                )
            }
        }
    }
}

/**
 * Process voice commands and determine which banking screen to show
 */
private fun processVoiceCommandInternal(
    currentState: BankingUIState,
    command: String
): BankingUIState {
    val lowerCommand = command.lowercase()
    
    Timber.d("Processing voice command: $command")

    val newState = when {
        // Account balance commands
        lowerCommand.contains("balance") || lowerCommand.contains("account balance") -> {
            currentState.copy(
                currentScreen = BankingScreen.AccountBalance,
                lastVoiceCommand = command
            )
        }

        // Transfer commands
        lowerCommand.contains("transfer") || lowerCommand.contains("send money") -> {
            val amount = extractAmountFromCommand(command)
            val transferState = if (amount != null) {
                currentState.fundTransfer.copy(amount = amount)
            } else {
                currentState.fundTransfer
            }
            currentState.copy(
                currentScreen = BankingScreen.FundTransfer,
                fundTransfer = transferState,
                lastVoiceCommand = command
            )
        }

        // Loan commands
        lowerCommand.contains("loan") || lowerCommand.contains("borrow") -> {
            val amount = extractAmountFromCommand(command)
            val loanType = extractLoanTypeFromCommand(command)
            val loanState = currentState.loanApplication.copy(
                requestedAmount = amount ?: currentState.loanApplication.requestedAmount,
                loanType = loanType ?: currentState.loanApplication.loanType
            )
            currentState.copy(
                currentScreen = BankingScreen.LoanApplication,
                loanApplication = loanState,
                lastVoiceCommand = command
            )
        }

        // Transaction history commands
        lowerCommand.contains("history") || lowerCommand.contains("transactions") -> {
            currentState.copy(
                currentScreen = BankingScreen.TransactionHistory,
                lastVoiceCommand = command
            )
        }

        // Account creation commands
        lowerCommand.contains("open account") || lowerCommand.contains("new account") -> {
            val accountType = extractAccountTypeFromCommand(command)
            val accountState = if (accountType != null) {
                currentState.accountCreation.copy(accountType = accountType)
            } else {
                currentState.accountCreation
            }
            currentState.copy(
                currentScreen = BankingScreen.AccountCreation,
                accountCreation = accountState,
                lastVoiceCommand = command
            )
        }

        // Identity verification commands
        lowerCommand.contains("verify") || lowerCommand.contains("authenticate") -> {
            currentState.copy(
                currentScreen = BankingScreen.IdentityVerification,
                lastVoiceCommand = command
            )
        }

        else -> {
            Timber.d("No matching banking command found for: $command")
            currentState.copy(lastVoiceCommand = command)
        }
    }

    return newState
}

/**
 * Extract monetary amount from voice command
 */
private fun extractAmountFromCommand(command: String): Double? {
    val patterns = listOf(
        Pattern.compile("\\$([0-9,]+(?:\\.[0-9]{2})?)"),
        Pattern.compile("([0-9,]+(?:\\.[0-9]{2})?) dollars?"),
        Pattern.compile("([0-9,]+) dollars?")
    )

    for (pattern in patterns) {
        val matcher = pattern.matcher(command)
        if (matcher.find()) {
            val amountStr = matcher.group(1)?.replace(",", "")
            return amountStr?.toDoubleOrNull()
        }
    }
    return null
}

/**
 * Extract loan type from voice command
 */
private fun extractLoanTypeFromCommand(command: String): LoanType? {
    val lowerCommand = command.lowercase()
    return when {
        lowerCommand.contains("personal") -> LoanType.PERSONAL
        lowerCommand.contains("mortgage") || lowerCommand.contains("home") -> LoanType.MORTGAGE
        lowerCommand.contains("auto") || lowerCommand.contains("car") -> LoanType.AUTO
        lowerCommand.contains("business") -> LoanType.BUSINESS
        lowerCommand.contains("student") -> LoanType.STUDENT
        else -> null
    }
}

/**
 * Extract account type from voice command
 */
private fun extractAccountTypeFromCommand(command: String): AccountType? {
    val lowerCommand = command.lowercase()
    return when {
        lowerCommand.contains("checking") -> AccountType.CHECKING
        lowerCommand.contains("savings") -> AccountType.SAVINGS
        lowerCommand.contains("business") -> AccountType.BUSINESS
        lowerCommand.contains("student") -> AccountType.STUDENT
        else -> null
    }
}

/**
 * Handle form submission for different banking screens
 */
private suspend fun handleFormSubmission(
    currentState: BankingUIState,
    screen: BankingScreen,
    rpcManager: RpcManager
): BankingUIState {
    return when (screen) {
        is BankingScreen.FundTransfer -> {
            // Handle fund transfer submission
            val transferState = currentState.fundTransfer.copy(isSubmitting = true)
            // TODO: Call RPC method for fund transfer
            currentState.copy(fundTransfer = transferState)
        }

        is BankingScreen.LoanApplication -> {
            // Handle loan application submission
            val loanState = currentState.loanApplication.copy(isSubmitting = true)
            // TODO: Call RPC method for loan application
            currentState.copy(loanApplication = loanState)
        }

        is BankingScreen.AccountCreation -> {
            // Handle account creation submission
            val accountState = currentState.accountCreation.copy(isSubmitting = true)
            // TODO: Call RPC method for account creation
            currentState.copy(accountCreation = accountState)
        }

        else -> currentState
    }
}
