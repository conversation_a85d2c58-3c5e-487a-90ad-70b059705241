package io.livekit.android.example.voiceassistant.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assistant
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.github.ajalt.timberkt.Timber
import io.livekit.android.example.voiceassistant.datastreams.ChatMessage
import io.livekit.android.example.voiceassistant.ui.banking.BankingScreen
import io.livekit.android.example.voiceassistant.ui.banking.BankingUIManager
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch

/**
 * Voice Assistant Overlay State
 */
data class VoiceAssistantOverlayState(
    val isVisible: Boolean = false,
    val isListening: Boolean = false,
    val transcriptionMessages: List<ChatMessage> = emptyList(),
    val currentTranscription: String = "",
    val fabPosition: Pair<Float, Float> = Pair(0f, 0f)
)

/**
 * Voice Assistant Overlay that appears when wake word is detected or FAB is pressed
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VoiceAssistantOverlay(
    isVisible: Boolean,
    isListening: Boolean,
    transcriptionMessages: List<ChatMessage>,
    currentTranscription: String,
    onDismiss: () -> Unit,
    onToggleListening: () -> Unit,
    bankingUIManager: BankingUIManager,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    
    // Animation for overlay visibility
    val overlayAlpha by animateFloatAsState(
        targetValue = if (isVisible) 0.7f else 0f,
        animationSpec = tween(300),
        label = "overlay_alpha"
    )
    
    // Process voice commands for banking navigation
    LaunchedEffect(transcriptionMessages) {
        transcriptionMessages.lastOrNull()?.let { lastMessage ->
            // Check if this is an agent response (not from user)
            if (!lastMessage.text.contains("User:")) {
                val messageText = lastMessage.text.lowercase()
                when {
                    messageText.contains("balance") || messageText.contains("account") -> {
                        Timber.d { "Navigating to Account Balance screen" }
                        bankingUIManager.navigateToScreen(BankingScreen.AccountBalance)
                    }
                    messageText.contains("transfer") || messageText.contains("send money") -> {
                        Timber.d { "Navigating to Fund Transfer screen" }
                        bankingUIManager.navigateToScreen(BankingScreen.FundTransfer)
                    }
                    messageText.contains("loan") || messageText.contains("borrow") -> {
                        Timber.d { "Navigating to Loan Application screen" }
                        bankingUIManager.navigateToScreen(BankingScreen.LoanApplication)
                    }
                    messageText.contains("history") || messageText.contains("transactions") -> {
                        Timber.d { "Navigating to Transaction History screen" }
                        bankingUIManager.navigateToScreen(BankingScreen.TransactionHistory)
                    }
                    messageText.contains("verify") || messageText.contains("identity") -> {
                        Timber.d { "Navigating to Identity Verification screen" }
                        bankingUIManager.navigateToScreen(BankingScreen.IdentityVerification)
                    }
                    messageText.contains("create account") || messageText.contains("new account") -> {
                        Timber.d { "Navigating to Account Creation screen" }
                        bankingUIManager.navigateToScreen(BankingScreen.AccountCreation)
                    }
                }
            }
        }
    }
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(animationSpec = tween(300)) + scaleIn(
            initialScale = 0.8f,
            animationSpec = tween(300)
        ),
        exit = fadeOut(animationSpec = tween(300)) + scaleOut(
            targetScale = 0.8f,
            animationSpec = tween(300)
        )
    ) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            Box(
                modifier = modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = overlayAlpha))
                    .clickable { onDismiss() }
            ) {
                // Main overlay content
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.8f)
                        .align(Alignment.Center)
                        .padding(16.dp)
                        .clickable(enabled = false) { }, // Prevent dismissal when clicking on card
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                    ) {
                        // Header
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Assistant,
                                    contentDescription = "Voice Assistant",
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(24.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Voice Assistant",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = FontWeight.Bold
                                )
                            }
                            
                            IconButton(onClick = onDismiss) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "Close",
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Status indicator
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = if (isListening) Icons.Default.Mic else Icons.Default.MicOff,
                                contentDescription = if (isListening) "Listening" else "Not listening",
                                tint = if (isListening) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = if (isListening) "Listening..." else "Tap to speak",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (isListening) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Transcription display
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surfaceVariant
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Column(
                                modifier = Modifier
                                    .fillMaxSize()
                                    .padding(12.dp)
                            ) {
                                Text(
                                    text = "Conversation",
                                    style = MaterialTheme.typography.titleMedium,
                                    fontWeight = FontWeight.SemiBold,
                                    modifier = Modifier.padding(bottom = 8.dp)
                                )
                                
                                val listState = rememberLazyListState()
                                
                                // Auto-scroll to latest message
                                LaunchedEffect(transcriptionMessages.size) {
                                    if (transcriptionMessages.isNotEmpty()) {
                                        listState.animateScrollToItem(transcriptionMessages.size - 1)
                                    }
                                }
                                
                                LazyColumn(
                                    state = listState,
                                    modifier = Modifier.weight(1f),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    items(transcriptionMessages) { message ->
                                        TranscriptionMessageItem(message = message)
                                    }
                                    
                                    // Show current transcription if available
                                    if (currentTranscription.isNotEmpty()) {
                                        item {
                                            TranscriptionMessageItem(
                                                message = ChatMessage.TextMessage(
                                                    id = "current",
                                                    text = currentTranscription,
                                                    identity = Participant.Identity("user"),
                                                    timestamp = System.currentTimeMillis(),
                                                    isFromUser = true
                                                ),
                                                isCurrentTranscription = true
                                            )
                                        }
                                    }
                                }
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Control buttons
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            // Toggle listening button
                            FloatingActionButton(
                                onClick = onToggleListening,
                                containerColor = if (isListening) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.secondary,
                                modifier = Modifier.size(56.dp)
                            ) {
                                Icon(
                                    imageVector = if (isListening) Icons.Default.MicOff else Icons.Default.Mic,
                                    contentDescription = if (isListening) "Stop listening" else "Start listening",
                                    tint = Color.White
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * Individual transcription message item
 */
@Composable
private fun TranscriptionMessageItem(
    message: ChatMessage,
    isCurrentTranscription: Boolean = false,
    modifier: Modifier = Modifier
) {
    val isUser = message.identity == Participant.Identity("user") || message.text.startsWith("User:")
    val displayText = if (message.text.startsWith("User:") || message.text.startsWith("Agent:")) {
        message.text.substringAfter(": ")
    } else {
        message.text
    }
    
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = if (isUser) Arrangement.End else Arrangement.Start
    ) {
        Card(
            modifier = Modifier
                .widthIn(max = 280.dp)
                .alpha(if (isCurrentTranscription) 0.7f else 1f),
            colors = CardDefaults.cardColors(
                containerColor = if (isUser) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.secondaryContainer
                }
            ),
            shape = RoundedCornerShape(
                topStart = 12.dp,
                topEnd = 12.dp,
                bottomStart = if (isUser) 12.dp else 4.dp,
                bottomEnd = if (isUser) 4.dp else 12.dp
            )
        ) {
            Text(
                text = displayText,
                modifier = Modifier.padding(12.dp),
                style = MaterialTheme.typography.bodyMedium,
                color = if (isUser) {
                    MaterialTheme.colorScheme.onPrimary
                } else {
                    MaterialTheme.colorScheme.onSecondaryContainer
                }
            )
        }
    }
}
