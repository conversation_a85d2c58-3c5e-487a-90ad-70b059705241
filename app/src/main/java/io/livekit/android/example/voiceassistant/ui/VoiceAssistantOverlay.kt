package io.livekit.android.example.voiceassistant.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Assistant
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import io.livekit.android.annotations.Beta
import io.livekit.android.compose.state.VoiceAssistant
import io.livekit.android.compose.ui.audio.VoiceAssistantBarVisualizer
import io.livekit.android.example.voiceassistant.datastreams.ChatMessage
import io.livekit.android.example.voiceassistant.ui.banking.BankingScreen
import io.livekit.android.example.voiceassistant.ui.banking.BankingUIManager
import io.livekit.android.example.voiceassistant.utils.BankAssistLogger
import io.livekit.android.room.participant.Participant
import kotlinx.coroutines.launch

/**
 * Voice Assistant Overlay State
 */
data class VoiceAssistantOverlayState(
    val isVisible: Boolean = false,
    val isListening: Boolean = false,
    val transcriptionMessages: List<ChatMessage> = emptyList(),
    val currentTranscription: String = "",
    val fabPosition: Pair<Float, Float> = Pair(0f, 0f)
)

/**
 * Voice Assistant Overlay that appears when wake word is detected or FAB is pressed
 */
@OptIn(ExperimentalMaterial3Api::class, Beta::class)
@Composable
fun VoiceAssistantOverlay(
    isVisible: Boolean,
    isListening: Boolean,
    transcriptionMessages: List<ChatMessage>,
    currentTranscription: String,
    onDismiss: () -> Unit,
    onToggleListening: () -> Unit,
    bankingUIManager: BankingUIManager,
    voiceAssistant: VoiceAssistant? = null, // VoiceAssistant for audio visualization
    room: Any? = null, // Room for determining local participant
    modifier: Modifier = Modifier
) {
    
    // Animation for overlay visibility
    val overlayAlpha by animateFloatAsState(
        targetValue = if (isVisible) 0.7f else 0f,
        animationSpec = tween(300),
        label = "overlay_alpha"
    )
    
    // Process voice commands for banking navigation - avoid duplicates
    LaunchedEffect(transcriptionMessages.size) {
        if (transcriptionMessages.isNotEmpty()) {
            val lastMessage = transcriptionMessages.last()
            // Only process agent responses, not user messages
            if (lastMessage.identity != Participant.Identity("user")) {
                val messageText = lastMessage.text.lowercase()
                when {
                    messageText.contains("balance") || messageText.contains("account") -> {
                        BankAssistLogger.logBankingOperation("Navigate to Account Balance")
                        bankingUIManager.navigateToScreen(BankingScreen.AccountBalance)
                    }
                    messageText.contains("transfer") || messageText.contains("send money") -> {
                        BankAssistLogger.logBankingOperation("Navigate to Fund Transfer")
                        bankingUIManager.navigateToScreen(BankingScreen.FundTransfer)
                    }
                    messageText.contains("loan") || messageText.contains("borrow") -> {
                        BankAssistLogger.logBankingOperation("Navigate to Loan Application")
                        bankingUIManager.navigateToScreen(BankingScreen.LoanApplication)
                    }
                    messageText.contains("history") || messageText.contains("transactions") -> {
                        BankAssistLogger.logBankingOperation("Navigate to Transaction History")
                        bankingUIManager.navigateToScreen(BankingScreen.TransactionHistory)
                    }
                    messageText.contains("verify") || messageText.contains("identity") -> {
                        BankAssistLogger.logBankingOperation("Navigate to Identity Verification")
                        bankingUIManager.navigateToScreen(BankingScreen.IdentityVerification)
                    }
                    messageText.contains("create account") || messageText.contains("new account") -> {
                        BankAssistLogger.logBankingOperation("Navigate to Account Creation")
                        bankingUIManager.navigateToScreen(BankingScreen.AccountCreation)
                    }
                }
            }
        }
    }
    
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(animationSpec = tween(300)) + scaleIn(
            initialScale = 0.8f,
            animationSpec = tween(300)
        ),
        exit = fadeOut(animationSpec = tween(300)) + scaleOut(
            targetScale = 0.8f,
            animationSpec = tween(300)
        )
    ) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                usePlatformDefaultWidth = false,
                dismissOnBackPress = true,
                dismissOnClickOutside = true
            )
        ) {
            Box(
                modifier = modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.7f)) // Fixed 70% transparency
                    .clickable { onDismiss() }
            ) {
                // Main overlay content - using original ChatTab structure
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight(0.9f)
                        .align(Alignment.Center)
                        .padding(16.dp)
                        .clickable(enabled = false) { }, // Prevent dismissal when clicking on card
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                    ) {
                        // Header with close button
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Assistant,
                                    contentDescription = "Voice Assistant",
                                    tint = MaterialTheme.colorScheme.primary,
                                    modifier = Modifier.size(24.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(
                                    text = "Voice Assistant",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = FontWeight.Bold
                                )
                            }

                            IconButton(onClick = onDismiss) {
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "Close",
                                    tint = MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // Original ChatTab structure with ConstraintLayout
                        ConstraintLayout(
                            modifier = Modifier
                                .fillMaxSize()
                                .weight(1f)
                        ) {
                            val (audioVisualizer, chatLog, chatInput) = createRefs()

                            // Voice Assistant Visualizer at the top (like original)
                            voiceAssistant?.let { assistant ->
                                VoiceAssistantBarVisualizer(
                                    voiceAssistant = assistant,
                                    modifier = Modifier
                                        .constrainAs(audioVisualizer) {
                                            height = Dimension.percent(0.1f)
                                            width = Dimension.percent(0.8f)
                                            top.linkTo(parent.top)
                                            start.linkTo(parent.start)
                                            end.linkTo(parent.end)
                                        }
                                        .padding(8.dp)
                                        .fillMaxWidth(),
                                    brush = SolidColor(MaterialTheme.colorScheme.primary)
                                )
                            }

                            // Chat display (like original ChatTab)
                            val lazyListState = rememberLazyListState()

                            // Auto-scroll to new messages
                            LaunchedEffect(transcriptionMessages.size) {
                                if (transcriptionMessages.isNotEmpty()) {
                                    lazyListState.animateScrollToItem(transcriptionMessages.size - 1)
                                }
                            }

                            LazyColumn(
                                userScrollEnabled = true,
                                state = lazyListState,
                                modifier = Modifier
                                    .constrainAs(chatLog) {
                                        top.linkTo(audioVisualizer.bottom, 8.dp)
                                        bottom.linkTo(chatInput.top, 8.dp)
                                        start.linkTo(parent.start)
                                        end.linkTo(parent.end)
                                        width = Dimension.fillToConstraints
                                        height = Dimension.fillToConstraints
                                    }
                                    .fillMaxSize()
                            ) {
                                // Add some sample messages for testing if no messages exist
                                if (transcriptionMessages.isEmpty()) {
                                    item {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(16.dp),
                                            contentAlignment = Alignment.Center
                                        ) {
                                            Text(
                                                text = "Start a conversation by speaking or typing a message",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }
                                }

                                items(
                                    items = transcriptionMessages,
                                    key = { message -> message.id },
                                ) { message ->
                                    ChatMessageItem(
                                        message = message,
                                        isFromUser = room?.let {
                                            message.identity == (it as io.livekit.android.room.Room).localParticipant.identity
                                        } ?: (message is ChatMessage.TextMessage && message.isFromUser),
                                        modifier = Modifier.animateItem()
                                    )
                                }
                            }

                            // Status indicator and controls at bottom
                            Row(
                                modifier = Modifier
                                    .constrainAs(chatInput) {
                                        bottom.linkTo(parent.bottom)
                                        start.linkTo(parent.start)
                                        end.linkTo(parent.end)
                                        width = Dimension.fillToConstraints
                                    }
                                    .fillMaxWidth()
                                    .padding(8.dp),
                                horizontalArrangement = Arrangement.SpaceEvenly,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Status indicator
                                Row(
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Icon(
                                        imageVector = if (isListening) Icons.Default.Mic else Icons.Default.MicOff,
                                        contentDescription = if (isListening) "Listening" else "Not listening",
                                        tint = if (isListening) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant,
                                        modifier = Modifier.size(20.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = if (isListening) "Listening..." else "Tap to speak",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = if (isListening) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }

                                // Toggle listening button
                                FloatingActionButton(
                                    onClick = onToggleListening,
                                    containerColor = if (isListening) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.secondary,
                                    modifier = Modifier.size(48.dp)
                                ) {
                                    Icon(
                                        imageVector = if (isListening) Icons.Default.MicOff else Icons.Default.Mic,
                                        contentDescription = if (isListening) "Stop listening" else "Start listening",
                                        tint = Color.White
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


